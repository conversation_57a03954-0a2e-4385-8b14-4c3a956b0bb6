package za.co.wethinkcode.robots.AcceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;
import za.co.wethinkcode.robots.server.ConfigLoader;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test that launches 10 robots on the reference server.
 * This test verifies that the reference server can handle multiple robot launches
 * and that each robot gets a unique position in the world.
 */
public class Launch10RobotsReferenceServerTest {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();
    private final ConfigLoader configLoader = new ConfigLoader();
    private Process serverProcess;

    @BeforeEach
    void startReferenceServerAndConnect() throws IOException {
        // Start reference server with 4x4 world (25 robot capacity)
        startReferenceServer("-s", "4");
        
        // Wait for server to start
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectAndStopServer() {
        try {
            if (serverClient.isConnected()) {
                serverClient.disconnect();
            }
        } catch (RuntimeException e) {
            // Server may have already disconnected - this is acceptable
            System.out.println("Server already disconnected: " + e.getMessage());
        }

        // Kill server process
        if (serverProcess != null && serverProcess.isAlive()) {
            serverProcess.destroyForcibly();
            try {
                serverProcess.waitFor();
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    private void startReferenceServer(String... args) throws IOException {
        // Use the latest reference server JAR
        String referenceServerJar = ".libs/reference-server-0.2.3.jar";
        
        ProcessBuilder pb = new ProcessBuilder();
        pb.command().add("java");
        pb.command().add("-jar");
        pb.command().add(referenceServerJar);
        
        // Add any additional arguments (like -s 4)
        for (String arg : args) {
            pb.command().add(arg);
        }
        
        serverProcess = pb.start();
        System.out.println("Started reference server with args: " + String.join(" ", args));
    }

    @Test
    @DisplayName("Launch 10 robots successfully on reference server")
    void launch10RobotsOnReferenceServer() {
        // Given that I am connected to the reference server
        // And the world is 4x4 (25 robot capacity)
        assertTrue(serverClient.isConnected());
        System.out.println("Connected to reference server");

        // When I launch 10 robots with unique names
        for (int i = 1; i <= 10; i++) {
            String robotName = "Robot" + i;
            String launchRequest = "{" +
                    "\"robot\": \"" + robotName + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            
            System.out.println("Launching robot: " + robotName);
            JsonNode response = serverClient.sendRequest(launchRequest);
            
            // Then each robot should launch successfully
            assertNotNull(response.get("result"), "Response should have a result field for " + robotName);
            assertEquals("OK", response.get("result").asText(), 
                    "Robot " + robotName + " should launch successfully. Response: " + response);
            
            // Verify response has data with position
            assertNotNull(response.get("data"), "Response should have data field for " + robotName);
            assertNotNull(response.get("data").get("position"), 
                    "Response should have position data for " + robotName);
            
            // Get and display robot position
            JsonNode position = response.get("data").get("position");
            int x = position.get(0).asInt();
            int y = position.get(1).asInt();
            System.out.println("Robot " + robotName + " launched at position [" + x + "," + y + "]");
            
            // Verify position is within world bounds (4x4 world has coordinates -2 to 2)
            assertTrue(x >= -2 && x <= 2, "X coordinate should be within world bounds for " + robotName);
            assertTrue(y >= -2 && y <= 2, "Y coordinate should be within world bounds for " + robotName);
        }
        
        System.out.println("Successfully launched all 10 robots on reference server!");
    }

    @Test
    @DisplayName("Verify world capacity after launching 10 robots")
    void verifyWorldCapacityAfterLaunching10Robots() {
        // Given that I am connected to the reference server
        assertTrue(serverClient.isConnected());
        
        // And I have successfully launched 10 robots
        for (int i = 1; i <= 10; i++) {
            String robotName = "TestRobot" + i;
            String launchRequest = "{" +
                    "\"robot\": \"" + robotName + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"tank\",\"5\",\"5\"]" +
                    "}";
            
            JsonNode response = serverClient.sendRequest(launchRequest);
            assertEquals("OK", response.get("result").asText(), 
                    "Robot " + robotName + " should launch successfully");
        }
        
        // When I try to launch additional robots (up to 15 more to test capacity)
        int successfulLaunches = 10; // We already launched 10
        for (int i = 11; i <= 25; i++) { // 4x4 world has 25 positions
            String robotName = "ExtraRobot" + i;
            String launchRequest = "{" +
                    "\"robot\": \"" + robotName + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"sniper\",\"5\",\"5\"]" +
                    "}";
            
            JsonNode response = serverClient.sendRequest(launchRequest);
            
            if ("OK".equals(response.get("result").asText())) {
                successfulLaunches++;
                System.out.println("Robot " + robotName + " launched successfully (total: " + successfulLaunches + ")");
            } else {
                System.out.println("Robot " + robotName + " failed to launch: " + response.get("data").get("message").asText());
                break; // Stop when we hit capacity
            }
        }
        
        // Then we should have launched at least 10 robots successfully
        assertTrue(successfulLaunches >= 10, 
                "Should have launched at least 10 robots, but only launched " + successfulLaunches);
        
        // And the world should eventually become full (25 robots max in 4x4 world)
        assertTrue(successfulLaunches <= 25, 
                "Should not be able to launch more than 25 robots in 4x4 world, but launched " + successfulLaunches);
        
        System.out.println("World capacity test completed. Total robots launched: " + successfulLaunches);
    }
}
