package za.co.wethinkcode.robots.AcceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;
import za.co.wethinkcode.robots.server.ConfigLoader;
import za.co.wethinkcode.robots.server.World;
import za.co.wethinkcode.robots.server.Obstacle;
import za.co.wethinkcode.robots.server.ObstacleType;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

public class LaunchRobot2x2Test {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();
    private final ConfigLoader configLoader = new ConfigLoader();
    private String url;
    private Process serverProcess;

    @BeforeEach
    void connectToServer() throws IOException {
        startServer();
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer() {
        try {
            serverClient.disconnect();
        } catch (RuntimeException e) {
            // Server may have already disconnected/crashed - this is acceptable
            System.out.println("Server already disconnected: " + e.getMessage());
        }

        // Kill the server process more aggressively
        if (serverProcess != null && serverProcess.isAlive()) {
            serverProcess.destroyForcibly();
            try {
                boolean terminated = serverProcess.waitFor(5, TimeUnit.SECONDS);
                if (!terminated) {
                    System.out.println("Process did not terminate gracefully, forcing port cleanup");
                    // Force kill using system commands if Java process cleanup fails
                    killProcessOnPort(DEFAULT_PORT);
                }
                Thread.sleep(1000); // Wait longer for cleanup
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Ensure port is free
        killProcessOnPort(DEFAULT_PORT);
    }

    // Helper methods to reduce duplication
    private void startServer(String... additionalArgs) throws IOException {
        // Load server url from properties file
        url = configLoader.loadServerUrl("serverConfig.properties");

        // Build server command with additional arguments
        List<String> command = new ArrayList<>();
        command.add("java");
        command.add("-jar");
        command.add(url);
        command.add("-s");
        command.add("2");
        command.addAll(Arrays.asList(additionalArgs));

        ProcessBuilder pb = new ProcessBuilder(command);
        serverProcess = pb.start(); // Store the process reference
        try {
            Thread.sleep(1000); // Wait for server to start
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private void killProcessOnPort(int port) {
        try {
            if (System.getProperty("os.name").toLowerCase().contains("windows")) {
                // Use netstat to find PID and taskkill to terminate
                ProcessBuilder pb = new ProcessBuilder("cmd", "/c",
                    "for /f \"tokens=5\" %a in ('netstat -ano ^| findstr :" + port + "') do taskkill /PID %a /F >nul 2>&1");
                Process killProcess = pb.start();
                killProcess.waitFor(3, TimeUnit.SECONDS);

                // Additional cleanup - wait a bit for port to be released
                Thread.sleep(500);
            } else {
                // Unix/Linux/Mac cleanup
                ProcessBuilder pb = new ProcessBuilder("bash", "-c",
                    "lsof -ti:" + port + " | xargs kill -9");
                Process killProcess = pb.start();
                killProcess.waitFor(3, TimeUnit.SECONDS);
                Thread.sleep(500);
            }
        } catch (Exception e) {
            System.out.println("Could not kill process on port " + port + ": " + e.getMessage());
        }
    }

    @Test
    void canLaunchAnotherRobotInLargerWorld() {
        // Given that I am connected to a Robot Worlds server
        // And the world is of size 2x2
        assertTrue(serverClient.isConnected());

        // And robot "HAL" has already been launched into the world
        String launchHAL = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode responseHAL = serverClient.sendRequest(launchHAL);
        assertEquals("OK", responseHAL.get("result").asText());

        // When I launch the robot "R2D2" into the world
        String launchR2D2 = "{" +
                "\"robot\": \"R2D2\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode responseR2D2 = serverClient.sendRequest(launchR2D2);

        // Then the launch should be successful
        assertEquals("OK", responseR2D2.get("result").asText());

        // And a position should be returned
        assertNotNull(responseR2D2.get("data"));
        assertNotNull(responseR2D2.get("data").get("position"));
        assertTrue(responseR2D2.get("data").get("position").isArray());
        assertEquals(2, responseR2D2.get("data").get("position").size());
    }

    @Test
    void worldWithoutObstaclesIsFull() {
        // Given that I am connected to a Robot Worlds server
        // And the world is of size 2x2
        assertTrue(serverClient.isConnected());

        // And I have successfully launched 9 robots into the world
        for (int i = 0; i < 9; i++) {
            String launchRequest = "{" +
                    "\"robot\": \"Robot" + i + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(launchRequest);
            assertEquals("OK", response.get("result").asText());
        }

        // When I try to launch one more robot
        String request = "{" +
                "\"robot\": \"ExtraRobot\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(request);

        // Then I should get an error response
        assertNotNull(response.get("result"));
        assertEquals("ERROR", response.get("result").asText());

        // And the message should indicate no more space
        assertNotNull(response.get("data"));
        assertNotNull(response.get("data").get("message"));
        assertEquals("No more space in this world", response.get("data").get("message").asText());
    }

    @Nested
    class WorldWithObstacleTests {

        @BeforeEach
        void setupObstacleWorld() throws IOException {
            // Disconnect from current server if connected
            if (serverClient.isConnected()) {
                serverClient.disconnect();
            }

            // Kill existing server process
            if (serverProcess != null && serverProcess.isAlive()) {
                serverProcess.destroyForcibly();
                try {
                    serverProcess.waitFor();
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            // Start server with obstacle configuration
            startServer("-o", "1,1");
            serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
        }

        @AfterEach
        void cleanupObstacleWorld() {
            // Disconnect from server
            if (serverClient.isConnected()) {
                try {
                    serverClient.disconnect();
                } catch (RuntimeException e) {
                    // Server may have already disconnected/crashed - this is acceptable
                    System.out.println("Server already disconnected: " + e.getMessage());
                }
            }

            // Kill the server process more aggressively
            if (serverProcess != null && serverProcess.isAlive()) {
                serverProcess.destroyForcibly();
                try {
                    boolean terminated = serverProcess.waitFor(5, TimeUnit.SECONDS);
                    if (!terminated) {
                        System.out.println("Process did not terminate gracefully, forcing port cleanup");
                        // Force kill using system commands if Java process cleanup fails
                        killProcessOnPort(DEFAULT_PORT);
                    }
                    Thread.sleep(1000); // Wait longer for cleanup
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            // Ensure port is free
            killProcessOnPort(DEFAULT_PORT);
        }

        @Test
        void worldWithObstacle() {
            // Given a world of size 2x2 with obstacle at [1,1]
            assertTrue(serverClient.isConnected());

            // When launching 8 robots (all available positions except obstacle)
            // First Launch 7 robots
            for (int i = 0; i < 7; i++) {
                String initialRequest = "{" +
                        "\"robot\": \"RoboCop" + i + "\"," +
                        "\"command\": \"launch\"," +
                        "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                        "}";
                JsonNode initialResponse = serverClient.sendRequest(initialRequest);
                assertEquals("OK", initialResponse.get("result").asText());

                // Then each robot cannot be in position [1,1]
                JsonNode initialPosition = initialResponse.get("data").get("position");
                assertFalse(initialPosition.get(0).asInt() == 1 && initialPosition.get(1).asInt() == 1);
            }

            // Launch 8th robot (PAL)
            String request = "{" +
                    "\"robot\": \"PAL\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"sniper\",\"5\",\"5\"]" +
                    "}";

            JsonNode response = serverClient.sendRequest(request);

            // Verifying the 8th robot
            assertNotNull(response.get("result"));
            assertEquals("OK", response.get("result").asText());
            assertNotNull(response.get("data"));
            assertNotNull(response.get("data").get("position"));

            // Then this robot cannot be in position [1,1]
            JsonNode position = response.get("data").get("position");
            assertFalse(position.get(0).asInt() == 1 && position.get(1).asInt() == 1);

        }

        @Test
        void worldWithObstacleIsFull() {
            // Given a world of size 2x2 with obstacle at [1,1]
            assertTrue(serverClient.isConnected());

            //And the world has an obstacle at coordinate [1,1]
            //And I have successfully launched 8 robots into the world
            for (int i = 0; i < 8; i++) {
                String initialRequest = "{" +
                        "\"robot\": \"Terminator" + i + "\"," +
                        "\"command\": \"launch\"," +
                        "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                        "}";
                JsonNode initialResponse = serverClient.sendRequest(initialRequest);
                assertEquals("OK", initialResponse.get("result").asText());
            }

            //When I launch one more robot
            String request = "{" +
                    "\"robot\": \"T1000\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(request);

            //Than I should get an error response back with the message "No more space in this world."
            assertNotNull(response.get("result"));
            assertEquals("ERROR", response.get("result").asText());
            assertNotNull(response.get("data"));
            assertNotNull(response.get("data").get("message"));
            assertEquals("No more space in this world", response.get("data").get("message").asText());
        }
    }
}