package za.co.wethinkcode.robots.AcceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;

import static org.junit.jupiter.api.Assertions.*;

/**
 * As a player
 * I want to get the state of a robot
 * So that I know its information at a given point in the game
 */
class  StateRobotTests {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer(){
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer(){
        serverClient.disconnect();
    }

    @Test
    void validStateShouldSucceed(){
        // Given that I am connected to a running Robot Worlds server
        // And the world is of size 1x1
        assertTrue(serverClient.isConnected());

        // And I have launched a robot successfully
        String launchRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"sniper\",\"0\",\"0\"]" +
                "}";

        JsonNode response = serverClient.sendRequest(launchRequest);
        assertNotNull(response.get("result"));
        assertEquals("OK", response.get("result").asText());

        // When I send a valid state request to the server
        String stateRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"state\"," +
                "\"arguments\": [\"sniper\",\"0\",\"0\"]" +
                "}";

        JsonNode stateResponse = serverClient.sendRequest(stateRequest);

        // Then I should get a valid response from the server
        assertEquals("OK", stateResponse.get("result").asText());

        // And I should get the state of the robot
        assertNotNull(response.get("state"));
    }
    @Test
    void invalidStateShouldFail() {
        // Given that I am connected to a running Robot World Server
        assertTrue(serverClient.isConnected());

        // When I ask for the state of a non-existent robot
        String stateRequest = "{" +
                "\"robot\": \"NON_EXISTENT_ROBOT\"," +
                "\"command\": \"state\"," +
                "\"arguments\": [\"tank\", \"5\", \"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(stateRequest);

        // Then the response should indicate an error
        assertEquals("ERROR", response.get("result").asText());
        assertNotNull(response.get("data").get("message"));
    }
}

