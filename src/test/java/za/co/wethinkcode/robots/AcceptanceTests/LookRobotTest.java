package za.co.wethinkcode.robots.AcceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;
import za.co.wethinkcode.robots.server.ConfigLoader;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

public class LookRobotTest {
    /**
     * As a Player,
     * I want my robot to be able to look around its surroundings.
     * So that I can avoid obstacles and walls, plan my next move, or find other robots nearby.
     */
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();
    private final ConfigLoader configLoader = new ConfigLoader();
    private Process serverProcess;

    @BeforeEach
    void connectToServer() throws IOException {
        String url = configLoader.loadServerUrl("serverConfig.properties");

        // Start a new server and store the process reference
        ProcessBuilder pb = new ProcessBuilder("java", "-jar", url);
        serverProcess = pb.start();

        // Wait for server to start
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer(){
        serverClient.disconnect();

        // Kill the server process
        if (serverProcess != null && serverProcess.isAlive()) {
            serverProcess.destroyForcibly();
            try {
                serverProcess.waitFor();
                Thread.sleep(500); // Wait for cleanup
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
    @Test
    void emptyWorld(){
        //Given I am connected to a Robot Worlds server
        assertTrue(serverClient.isConnected());
        //And my robot has successfully launched at position (0,0) into an empty world
        String launchRequest = "{" +
                "\"robot\": \"WallE\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"sniper\",\"5\",\"5\"]" +
                "}";

        JsonNode response = serverClient.sendRequest(launchRequest);
        assertNotNull(response.get("result"));
        assertEquals("OK", response.get("result").asText());

        //When I send look request to the server
        String lookRequest = "{" +
                "\"robot\": \"WallE\"," +
                "\"command\": \"look\"," +
                "\"arguments\": []" +
                "}";
        response = serverClient.sendRequest(lookRequest);

        //Then I should get a response that shows an empty list of obstacles from the server
        assertEquals("OK", response.get("result").asText());
        JsonNode data = response.get("data");
        assertNotNull(data);

        JsonNode objects = data.get("objects");
        assertNotNull(objects);
        assertTrue(objects.isArray());
        assertEquals(4, objects.size());

        //And the position of the robot should be (0,0)
        assertNotNull(data.get("position"));
        assertEquals(0, data.get("position").get(0).asInt());
        assertEquals(0, data.get("position").get(1).asInt());

        //And I should also get the state and direction the robot is facing
        JsonNode state = response.get("state");
        assertNotNull(state);
        assertNotNull(state.get("direction"));
    }

//    @Test
//    void lookObjectView(){
//        //Given I am connected to a Robot Worlds server
//
//        //And my robot has successfully launched at position (0,0) into a world with objects in the direction I'm facing
//
//        //When I send look request to the server
//
//        //Then I should get a response that shows a list of obstacles (with at least one obstacle) in the direction I'm facing from the server
//
//        //And the obstacle type and the distance of the obstacle from the server
//
//        //And I should also get the state and the direction of my robot
//    }
//
//    @Test
//    void lookRobotInView(){
//        //Given I am connected to a Robot Worlds server
//
//        //And my robot has successfully launched at position (0,0) into a world
//
//        //And another robot is also successfully launched at position (1,0)
//
//        //When I send look request to the server
//
//        //Then I should get a response that shows a list of obstacles (the other Robots) in the direction I'm facing from the server
//
//        //And the name of the robot in my view and the distance
//
//        //And I should also get the state and direction of my robot
//
//    }
}
